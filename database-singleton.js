/**
 * وحدة إدارة قاعدة البيانات باستخدام نمط التصميم Singleton
 * تضمن هذه الوحدة وجود نسخة واحدة فقط من اتصال قاعدة البيانات في جميع أنحاء التطبيق
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const { logError, logSystem } = require('./error-handler');

// تعريف متغير خاص للاحتفاظ بنسخة قاعدة البيانات
let _instance = null;
let _database = null;

/**
 * فئة إدارة قاعدة البيانات باستخدام نمط التصميم Singleton
 */
class DatabaseManager {
  /**
   * الحصول على نسخة قاعدة البيانات
   * @returns {Object} - كائن قاعدة البيانات
   */
  static getInstance() {
    if (!_instance) {
      _instance = new DatabaseManager();
    }
    return _instance;
  }

  /**
   * الحصول على اتصال قاعدة البيانات
   * @returns {Object} - كائن اتصال قاعدة البيانات
   */
  getConnection() {
    return _database;
  }

  /**
   * تهيئة قاعدة البيانات
   * @param {string} dbPath - مسار ملف قاعدة البيانات
   * @returns {Promise<Object>} - وعد يحتوي على كائن قاعدة البيانات
   */
  async initialize(dbPath) {
    try {
      console.log('بدء تشغيل ملفات السجل القديمة');
      this._cleanupOldLogFiles();

      console.log('تم استيراد معالجات الأخطاء');
      logSystem('بدء تهيئة قاعدة البيانات', 'info');

      // التأكد من وجود مجلد قاعدة البيانات
      const dbDir = path.dirname(dbPath);
      if (!fs.existsSync(dbDir)) {
        console.log(`إنشاء مجلد قاعدة البيانات: ${dbDir}`);
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // استيراد وحدة better-sqlite3
      console.log('جاري تهيئة قاعدة بيانات SQLite...');
      console.log(`مسار قاعدة بيانات SQLite: ${dbPath}`);

      try {
        const Database = require('better-sqlite3');
        _database = new Database(dbPath, { verbose: console.log });

        // تفعيل دعم المفاتيح الخارجية
        _database.pragma('foreign_keys = ON');

        // تحسين أداء قاعدة البيانات وضمان حفظ البيانات
        _database.pragma('synchronous = FULL');  // ضمان حفظ البيانات بشكل كامل
        _database.pragma('journal_mode = WAL');  // استخدام وضع WAL لتحسين الأداء وضمان سلامة البيانات
        _database.pragma('temp_store = MEMORY'); // تخزين الجداول المؤقتة في الذاكرة
        _database.pragma('cache_size = 10000');  // زيادة حجم ذاكرة التخزين المؤقت

        // تعيين حجم ملف WAL
        _database.pragma('wal_autocheckpoint = 100'); // تنفيذ checkpoint بعد 100 صفحة

        // تعيين وضع المزامنة للملف الرئيسي وملف WAL
        _database.pragma('synchronous = FULL');
        _database.pragma('wal_synchronous = FULL');

        // تنفيذ checkpoint أولي
        _database.pragma('wal_checkpoint(RESTART)');

        console.log('تم الاتصال بقاعدة بيانات SQLite بنجاح وتحسين الإعدادات');
        logSystem('تم تهيئة قاعدة البيانات بنجاح مع إعدادات محسنة', 'info');

        // إنشاء الجداول إذا لم تكن موجودة
        this._createTables();

        // تحديث هيكل قاعدة البيانات (إضافة أعمدة جديدة وتعديلات أخرى)
        this.updateDatabaseSchema();

        return _database;
      } catch (error) {
        console.error('خطأ في تهيئة قاعدة بيانات SQLite:', error);
        logError(error, 'تهيئة قاعدة البيانات');
        throw error;
      }
    } catch (error) {
      console.error('خطأ في تهيئة قاعدة البيانات:', error);
      logError(error, 'تهيئة قاعدة البيانات');
      throw error;
    }
  }

  /**
   * إغلاق اتصال قاعدة البيانات
   */
  close() {
    if (_database) {
      try {
        // ضمان حفظ جميع التغييرات قبل الإغلاق
        try {
          console.log('جاري حفظ جميع التغييرات في قاعدة البيانات قبل الإغلاق...');

          // تعيين وضع المزامنة إلى FULL لضمان حفظ البيانات
          _database.pragma('synchronous = FULL');

          // تنفيذ checkpoint كامل
          _database.prepare('PRAGMA wal_checkpoint(FULL)').run();

          // تنفيذ checkpoint آخر للتأكد
          _database.prepare('PRAGMA wal_checkpoint(RESTART)').run();

          // إجبار حفظ البيانات على القرص
          _database.exec('PRAGMA synchronous = FULL');

          console.log('تم حفظ جميع التغييرات في قاعدة البيانات قبل الإغلاق');
          logSystem('تم حفظ جميع التغييرات في قاعدة البيانات قبل الإغلاق', 'info');
        } catch (checkpointError) {
          console.warn('تحذير: قد تكون هناك مشكلة في حفظ التغييرات قبل الإغلاق:', checkpointError);
          logSystem('تحذير: قد تكون هناك مشكلة في حفظ التغييرات قبل الإغلاق: ' + checkpointError.message, 'warning');
        }

        // إغلاق قاعدة البيانات
        console.log('جاري إغلاق اتصال قاعدة البيانات...');
        _database.close();
        console.log('تم إغلاق اتصال قاعدة البيانات بنجاح');
        logSystem('تم إغلاق اتصال قاعدة البيانات بنجاح', 'info');
      } catch (error) {
        console.error('خطأ في إغلاق اتصال قاعدة البيانات:', error);
        logError(error, 'إغلاق قاعدة البيانات');
      } finally {
        _database = null;
      }
    }
  }

  /**
   * ضمان حفظ البيانات بشكل دوري
   * يتم استدعاء هذه الوظيفة بشكل دوري لضمان حفظ البيانات
   */
  ensureDataPersistence() {
    if (_database) {
      try {
        console.log('جاري تنفيذ حفظ دوري للبيانات...');

        // تنفيذ checkpoint
        _database.prepare('PRAGMA wal_checkpoint(PASSIVE)').run();

        console.log('تم تنفيذ حفظ دوري للبيانات بنجاح');
        return true;
      } catch (error) {
        console.error('خطأ في تنفيذ حفظ دوري للبيانات:', error);
        logError(error, 'ensureDataPersistence');
        return false;
      }
    }
    return false;
  }

  /**
   * تنظيف ملفات السجل القديمة
   * @private
   */
  _cleanupOldLogFiles() {
    try {
      const logDir = path.join(app.getPath('userData'), 'logs');
      if (fs.existsSync(logDir)) {
        const files = fs.readdirSync(logDir);
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        for (const file of files) {
          if (file.endsWith('.log')) {
            const filePath = path.join(logDir, file);
            const stats = fs.statSync(filePath);
            if (stats.mtime < oneWeekAgo) {
              fs.unlinkSync(filePath);
              console.log(`تم حذف ملف السجل القديم: ${file}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('خطأ في تنظيف ملفات السجل القديمة:', error);
    }
  }

  /**
   * تحديث هيكل قاعدة البيانات
   * إضافة حقول جديدة وتعديلات أخرى على هيكل قاعدة البيانات
   * @returns {boolean} - نجاح العملية
   */
  updateDatabaseSchema() {
    try {
      if (!_database) {
        console.warn('لم يتم تهيئة قاعدة البيانات. لا يمكن تحديث الهيكل.');
        return false;
      }

      console.log('جاري تحديث هيكل قاعدة البيانات...');
      logSystem('جاري تحديث هيكل قاعدة البيانات...', 'info');

      // التحقق من هيكل جدول الأصناف وإضافة الأعمدة المفقودة
      this._ensureTableColumns('items', [
        { name: 'name', type: 'TEXT NOT NULL' },
        { name: 'unit', type: 'TEXT' },
        { name: 'initial_quantity', type: 'INTEGER', default: '0' },
        { name: 'avg_price', type: 'REAL', default: '0' },
        { name: 'selling_price', type: 'REAL', default: '0' },
        { name: 'created_at', type: 'TEXT', default: 'CURRENT_TIMESTAMP' },
        { name: 'updated_at', type: 'TEXT', default: 'CURRENT_TIMESTAMP' }
      ]);

      // التحقق من هيكل جدول المخزون وإضافة الأعمدة المفقودة
      this._ensureTableColumns('inventory', [
        { name: 'item_id', type: 'INTEGER NOT NULL' },
        { name: 'current_quantity', type: 'INTEGER', default: '0' },
        { name: 'minimum_quantity', type: 'INTEGER', default: '0' },
        { name: 'avg_price', type: 'REAL', default: '0' },
        { name: 'selling_price', type: 'REAL', default: '0' },
        { name: 'last_updated', type: 'TEXT', default: 'CURRENT_TIMESTAMP' }
      ]);

      // التحقق من هيكل جدول المعاملات وإضافة الأعمدة المفقودة
      this._ensureTableColumns('transactions', [
        { name: 'transaction_id', type: 'TEXT NOT NULL' },
        { name: 'item_id', type: 'INTEGER' },
        { name: 'transaction_type', type: 'TEXT NOT NULL' },
        { name: 'quantity', type: 'INTEGER NOT NULL' },
        { name: 'price', type: 'REAL', default: '0' },
        { name: 'selling_price', type: 'REAL', default: '0' },
        { name: 'total_price', type: 'REAL', default: '0' },
        { name: 'profit', type: 'REAL', default: '0' },
        { name: 'transport_cost', type: 'REAL', default: '0' },
        { name: 'customer', type: 'TEXT' },
        { name: 'customer_id', type: 'INTEGER' },
        { name: 'invoice_number', type: 'TEXT' },
        { name: 'parent_invoice_number', type: 'TEXT' },
        { name: 'notes', type: 'TEXT' },
        { name: 'skip_inventory_update', type: 'INTEGER', default: '0' },
        { name: 'transaction_date', type: 'TEXT', default: 'CURRENT_TIMESTAMP' },
        { name: 'user_id', type: 'INTEGER' }
      ]);

      // التحقق من هيكل جدول العملاء وإضافة الأعمدة المفقودة
      this._ensureTableColumns('customers', [
        { name: 'name', type: 'TEXT NOT NULL' },
        { name: 'contact_person', type: 'TEXT' },
        { name: 'phone', type: 'TEXT' },
        { name: 'email', type: 'TEXT' },
        { name: 'address', type: 'TEXT' },
        { name: 'customer_type', type: 'TEXT', default: "'normal'" },
        { name: 'parent_id', type: 'INTEGER' },
        { name: 'credit_limit', type: 'REAL', default: '0' },
        { name: 'balance', type: 'REAL', default: '0' },
        { name: 'created_at', type: 'TEXT', default: 'CURRENT_TIMESTAMP' },
        { name: 'updated_at', type: 'TEXT', default: 'CURRENT_TIMESTAMP' },
        { name: 'main_invoice_id', type: 'TEXT' }
      ]);

      // التحقق من هيكل جدول الخزينة وإضافة الأعمدة المفقودة
      this._ensureTableColumns('cashbox', [
        { name: 'initial_balance', type: 'REAL NOT NULL' },
        { name: 'current_balance', type: 'REAL NOT NULL' },
        { name: 'profit_total', type: 'REAL', default: '0' },
        { name: 'sales_total', type: 'REAL', default: '0' },
        { name: 'purchases_total', type: 'REAL', default: '0' },
        { name: 'returns_total', type: 'REAL', default: '0' },
        { name: 'transport_total', type: 'REAL', default: '0' },
        { name: 'created_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
        { name: 'updated_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' }
      ]);

      // إنشاء فهرس للبحث السريع عن الفواتير الفرعية
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_parent_invoice ON transactions(parent_invoice_number)');

      // التحقق من وجود جدول سجل مبيعات العملاء
      const customerSalesHistoryTableExists = _database.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='customer_sales_history'").get();
      if (!customerSalesHistoryTableExists) {
        console.log('جدول customer_sales_history غير موجود، جاري إنشاؤه...');

        // إنشاء جدول سجل مبيعات العملاء
        _database.exec(`
          CREATE TABLE IF NOT EXISTS customer_sales_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER NOT NULL,
            transaction_id INTEGER,
            invoice_number TEXT,
            total_amount REAL NOT NULL DEFAULT 0,
            total_profit REAL NOT NULL DEFAULT 0,
            transaction_date TEXT NOT NULL,
            notes TEXT,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
            FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE SET NULL
          )
        `);

        // إنشاء فهارس لجدول سجل مبيعات العملاء
        _database.exec('CREATE INDEX IF NOT EXISTS idx_customer_sales_history_customer_id ON customer_sales_history(customer_id)');
        _database.exec('CREATE INDEX IF NOT EXISTS idx_customer_sales_history_transaction_id ON customer_sales_history(transaction_id)');
        _database.exec('CREATE INDEX IF NOT EXISTS idx_customer_sales_history_invoice_number ON customer_sales_history(invoice_number)');

        console.log('تم إنشاء جدول customer_sales_history بنجاح');

        // ملء جدول سجل مبيعات العملاء من المعاملات الحالية
        try {
          _database.exec(`
            INSERT OR IGNORE INTO customer_sales_history (
              customer_id, transaction_id, invoice_number, total_amount, total_profit, transaction_date, notes
            )
            SELECT
              customer_id, id, invoice_number, total_price, profit, transaction_date, notes
            FROM transactions
            WHERE transaction_type = 'sale' AND customer_id IS NOT NULL;
          `);
          console.log('تم ملء جدول customer_sales_history بالبيانات الحالية');
        } catch (fillError) {
          console.error('خطأ في ملء جدول customer_sales_history:', fillError);
          logError(fillError, 'updateDatabaseSchema - fill customer_sales_history');
        }
      }

      console.log('تم تحديث هيكل قاعدة البيانات بنجاح');
      logSystem('تم تحديث هيكل قاعدة البيانات بنجاح', 'info');
      return true;
    } catch (error) {
      console.error('خطأ في تحديث هيكل قاعدة البيانات:', error);
      logError(error, 'updateDatabaseSchema');
      return false;
    }
  }

  /**
   * التأكد من وجود الأعمدة المطلوبة في جدول معين
   * @param {string} tableName - اسم الجدول
   * @param {Array} columns - مصفوفة من الأعمدة المطلوبة
   * @private
   */
  _ensureTableColumns(tableName, columns) {
    try {
      // التحقق من وجود الجدول
      const tableExistsStmt = _database.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?");
      const tableExists = tableExistsStmt.get(tableName);

      if (!tableExists) {
        console.log(`جدول ${tableName} غير موجود، سيتم إنشاؤه في _createTables`);
        return;
      }

      // الحصول على معلومات الجدول
      const tableInfo = _database.prepare(`PRAGMA table_info(${tableName})`).all();

      // التحقق من كل عمود مطلوب
      for (const column of columns) {
        const columnExists = tableInfo.some(col => col.name === column.name);

        if (!columnExists) {
          console.log(`جاري إضافة عمود ${column.name} إلى جدول ${tableName}...`);

          // تحديد نص الإضافة
          let alterSql = `ALTER TABLE ${tableName} ADD COLUMN ${column.name} ${column.type}`;

          // إضافة القيمة الافتراضية إذا كانت موجودة
          if (column.default) {
            alterSql += ` DEFAULT ${column.default}`;
          }

          // تنفيذ الاستعلام
          _database.exec(alterSql);

          console.log(`تم إضافة عمود ${column.name} إلى جدول ${tableName} بنجاح`);
          logSystem(`تم إضافة عمود ${column.name} إلى جدول ${tableName}`, 'info');
        }
      }
    } catch (error) {
      console.error(`خطأ في التحقق من أعمدة جدول ${tableName}:`, error);
      logError(error, `_ensureTableColumns - ${tableName}`);
    }
  }

  /**
   * إنشاء جداول قاعدة البيانات
   * @private
   */
  _createTables() {
    try {
      if (!_database) {
        console.warn('لم يتم تهيئة قاعدة البيانات. لا يمكن إنشاء الجداول.');
        return false;
      }

      console.log('جاري إنشاء جداول قاعدة البيانات...');

      // إنشاء جدول المستخدمين
      _database.exec(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          full_name TEXT,
          role TEXT NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول الأصناف
      _database.exec(`
        CREATE TABLE IF NOT EXISTS items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          unit TEXT,
          initial_quantity INTEGER DEFAULT 0,
          avg_price REAL DEFAULT 0,
          selling_price REAL DEFAULT 0,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول المخزون
      _database.exec(`
        CREATE TABLE IF NOT EXISTS inventory (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          current_quantity INTEGER DEFAULT 0,
          minimum_quantity INTEGER DEFAULT 0,
          avg_price REAL DEFAULT 0,
          selling_price REAL DEFAULT 0,
          last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE
        )
      `);

      // إنشاء جدول المعاملات
      _database.exec(`
        CREATE TABLE IF NOT EXISTS transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transaction_id TEXT UNIQUE NOT NULL,
          item_id INTEGER,
          transaction_type TEXT NOT NULL,
          quantity INTEGER NOT NULL,
          price REAL DEFAULT 0,
          selling_price REAL DEFAULT 0,
          total_price REAL DEFAULT 0,
          profit REAL DEFAULT 0,
          transport_cost REAL DEFAULT 0,
          customer TEXT,
          customer_id INTEGER,
          invoice_number TEXT,
          parent_invoice_number TEXT,
          notes TEXT,
          skip_inventory_update INTEGER DEFAULT 0,
          transaction_date TEXT DEFAULT CURRENT_TIMESTAMP,
          user_id INTEGER,
          FOREIGN KEY (item_id) REFERENCES items (id),
          FOREIGN KEY (user_id) REFERENCES users (id),
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL
        )
      `);

      // إنشاء جدول العملاء
      _database.exec(`
        CREATE TABLE IF NOT EXISTS customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          contact_person TEXT,
          phone TEXT,
          email TEXT,
          address TEXT,
          customer_type TEXT DEFAULT 'normal',
          parent_id INTEGER,
          credit_limit REAL DEFAULT 0,
          balance REAL DEFAULT 0,
          main_invoice_id TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (parent_id) REFERENCES customers (id) ON DELETE SET NULL
        )
      `);

      // إنشاء جدول الآلات
      _database.exec(`
        CREATE TABLE IF NOT EXISTS machines (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          purchase_date TEXT NOT NULL,
          purchase_price REAL NOT NULL,
          current_value REAL NOT NULL,
          notes TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول الإعدادات
      _database.exec(`
        CREATE TABLE IF NOT EXISTS settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول الخزينة
      _database.exec(`
        CREATE TABLE IF NOT EXISTS cashbox (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          initial_balance REAL NOT NULL,
          current_balance REAL NOT NULL,
          profit_total REAL DEFAULT 0,
          sales_total REAL DEFAULT 0,
          purchases_total REAL DEFAULT 0,
          returns_total REAL DEFAULT 0,
          transport_total REAL DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // تم إزالة جدول معاملات الخزينة - لا نحتاجه لأن المعاملات تُخزن في جدول transactions

      // إنشاء فهارس للأصناف
      _database.exec('CREATE INDEX IF NOT EXISTS idx_items_name ON items(name)');

      // إنشاء فهارس للمخزون
      _database.exec('CREATE INDEX IF NOT EXISTS idx_inventory_item_id ON inventory(item_id)');

      // إنشاء فهارس للمعاملات
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_transaction_id ON transactions(transaction_id)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_item_id ON transactions(item_id)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_invoice ON transactions(invoice_number)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_transactions_parent_invoice ON transactions(parent_invoice_number)');

      // إنشاء فهارس للعملاء
      _database.exec('CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(customer_type)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_customers_parent ON customers(parent_id)');

      // إنشاء فهارس للآلات
      _database.exec('CREATE INDEX IF NOT EXISTS idx_machines_name ON machines(name)');

      // إنشاء فهارس للإعدادات
      _database.exec('CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key)');

      // إنشاء فهارس للخزينة
      _database.exec('CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_type ON cashbox_transactions(type)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_created_at ON cashbox_transactions(created_at)');

      // إنشاء جدول سجل مبيعات العملاء
      _database.exec(`
        CREATE TABLE IF NOT EXISTS customer_sales_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER NOT NULL,
          transaction_id INTEGER,
          invoice_number TEXT,
          total_amount REAL NOT NULL DEFAULT 0,
          total_profit REAL NOT NULL DEFAULT 0,
          transaction_date TEXT NOT NULL,
          notes TEXT,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
          FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE SET NULL
        )
      `);

      // إنشاء فهارس لجدول سجل مبيعات العملاء
      _database.exec('CREATE INDEX IF NOT EXISTS idx_customer_sales_history_customer_id ON customer_sales_history(customer_id)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_customer_sales_history_transaction_id ON customer_sales_history(transaction_id)');
      _database.exec('CREATE INDEX IF NOT EXISTS idx_customer_sales_history_invoice_number ON customer_sales_history(invoice_number)');

      console.log('تم إنشاء جداول قاعدة البيانات بنجاح');
      return true;
    } catch (error) {
      console.error('خطأ في إنشاء جداول قاعدة البيانات:', error);
      logError(error, '_createTables');
      return false;
    }
  }

  /**
   * تنفيذ استعلام مع معالجة الأخطاء
   * @param {Function} callback - دالة تنفذ الاستعلام
   * @param {string} operation - اسم العملية للتسجيل
   * @returns {*} - نتيجة الاستعلام
   */
  executeQuery(callback, operation) {
    try {
      if (!_database) {
        throw new Error('قاعدة البيانات غير متصلة');
      }
      return callback(_database);
    } catch (error) {
      logError(error, `executeQuery - ${operation}`);
      throw error;
    }
  }

  /**
   * تنفيذ استعلام داخل معاملة
   * @param {Function} callback - دالة تنفذ الاستعلام
   * @param {string} operation - اسم العملية للتسجيل
   * @returns {*} - نتيجة الاستعلام
   */
  executeTransaction(callback, operation) {
    try {
      if (!_database) {
        throw new Error('قاعدة البيانات غير متصلة');
      }

      _database.exec('BEGIN TRANSACTION');
      try {
        const result = callback(_database);
        _database.exec('COMMIT');

        // ضمان حفظ التغييرات بعد الالتزام بالمعاملة
        try {
          _database.prepare('PRAGMA wal_checkpoint(FULL)').run();
          console.log(`تم حفظ التغييرات في قاعدة البيانات بعد معاملة "${operation}"`);
        } catch (checkpointError) {
          console.warn(`تحذير: قد تكون هناك مشكلة في حفظ التغييرات بعد معاملة "${operation}":`, checkpointError);
          logSystem(`تحذير: قد تكون هناك مشكلة في حفظ التغييرات بعد معاملة "${operation}": ` + checkpointError.message, 'warning');
        }

        return result;
      } catch (error) {
        _database.exec('ROLLBACK');
        logError(error, `executeTransaction - ${operation}`);
        throw error;
      }
    } catch (error) {
      logError(error, `executeTransaction - ${operation}`);
      throw error;
    }
  }
}

module.exports = DatabaseManager;
