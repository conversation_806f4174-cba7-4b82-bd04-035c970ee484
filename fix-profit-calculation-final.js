/**
 * إصلاح نهائي لحساب الأرباح مع مصاريف النقل
 * يضمن أن مصاريف النقل في المشتريات لا تؤثر على الأرباح
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.db';

console.log('🔧 إصلاح نهائي لحساب الأرباح مع مصاريف النقل');
console.log('=' .repeat(60));

async function fixProfitCalculation() {
  let db;

  try {
    // الاتصال بقاعدة البيانات
    db = new Database(dbPath);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. عرض حالة الخزينة الحالية
    console.log('\n📊 حالة الخزينة الحالية:');
    const currentCashbox = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    if (currentCashbox) {
      console.log(`   الرصيد الحالي: ${currentCashbox.current_balance} د.ل`);
      console.log(`   إجمالي المبيعات: ${currentCashbox.sales_total} د.ل`);
      console.log(`   إجمالي المشتريات: ${currentCashbox.purchases_total} د.ل`);
      console.log(`   إجمالي مصاريف النقل: ${currentCashbox.transport_total || 0} د.ل`);
      console.log(`   إجمالي الأرباح الحالي: ${currentCashbox.profit_total} د.ل`);
    }

    // 2. إعادة حساب الأرباح بالطريقة الصحيحة
    console.log('\n🔄 إعادة حساب الأرباح بالطريقة الصحيحة...');

    // الحصول على جميع معاملات البيع
    const salesStmt = db.prepare(`
      SELECT
        t.id,
        t.item_id,
        t.quantity,
        t.selling_price,
        t.profit as stored_profit,
        i.avg_price
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale'
        AND t.selling_price > 0
    `);

    const salesTransactions = salesStmt.all();
    let totalCalculatedProfit = 0;

    console.log(`   معالجة ${salesTransactions.length} معاملة بيع...`);

    // إعادة حساب الربح لكل معاملة بيع مع مراعاة مصاريف النقل
    for (const transaction of salesTransactions) {
      try {
        const { item_id, quantity, selling_price, avg_price, stored_profit } = transaction;

        // التحقق من وجود متوسط سعر الشراء
        if (!avg_price || avg_price <= 0) {
          console.warn(`     متوسط سعر الشراء غير متوفر للصنف ${item_id}, استخدام الربح المحفوظ: ${stored_profit || 0}`);
          totalCalculatedProfit += Number(stored_profit || 0);
          continue;
        }

        // حساب مصاريف النقل المخصصة لهذا الصنف من عمليات الشراء
        let transportCostPerUnit = 0;
        try {
          const purchaseStmt = db.prepare(`
            SELECT quantity, transport_cost
            FROM transactions
            WHERE item_id = ?
              AND transaction_type = 'purchase'
              AND transport_cost > 0
          `);

          const purchases = purchaseStmt.all(item_id);
          if (purchases && purchases.length > 0) {
            let totalPurchasedQuantity = 0;
            let totalTransportCost = 0;

            purchases.forEach(purchase => {
              totalPurchasedQuantity += purchase.quantity || 0;
              totalTransportCost += purchase.transport_cost || 0;
            });

            if (totalPurchasedQuantity > 0) {
              transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
            }
          }
        } catch (transportError) {
          console.error(`     خطأ في حساب مصاريف النقل للصنف ${item_id}:`, transportError);
          transportCostPerUnit = 0;
        }

        // حساب الربح الصحيح: (سعر البيع - متوسط سعر الشراء - مصاريف النقل لكل وحدة) × الكمية
        const calculatedProfit = (selling_price - avg_price - transportCostPerUnit) * quantity;

        if (transportCostPerUnit > 0) {
          console.log(`     الصنف ${item_id}: (${selling_price} - ${avg_price} - ${transportCostPerUnit}) × ${quantity} = ${calculatedProfit.toFixed(2)}`);
        }

        totalCalculatedProfit += calculatedProfit;

      } catch (transactionError) {
        console.error(`     خطأ في معالجة المعاملة ${transaction.id}:`, transactionError);
        // في حالة الخطأ، استخدم الربح المحفوظ
        totalCalculatedProfit += Number(transaction.stored_profit || 0);
      }
    }

    // خصم أرباح المرتجعات
    const returnsStmt = db.prepare(`
      SELECT COALESCE(SUM(ABS(profit)), 0) as total_return_profit
      FROM transactions
      WHERE transaction_type = 'return'
    `);

    const returnsResult = returnsStmt.get();
    const totalReturnProfit = Number(returnsResult ? returnsResult.total_return_profit : 0);

    // الربح النهائي = أرباح المبيعات - أرباح المرتجعات
    const finalTotalProfit = totalCalculatedProfit - totalReturnProfit;

    console.log('\n📈 نتائج إعادة الحساب:');
    console.log(`   إجمالي أرباح المبيعات: ${totalCalculatedProfit.toFixed(2)} د.ل`);
    console.log(`   إجمالي أرباح المرتجعات: ${totalReturnProfit.toFixed(2)} د.ل`);
    console.log(`   إجمالي الأرباح النهائي: ${finalTotalProfit.toFixed(2)} د.ل`);
    console.log(`   الأرباح السابقة: ${currentCashbox.profit_total} د.ل`);
    console.log(`   الفرق: ${(finalTotalProfit - currentCashbox.profit_total).toFixed(2)} د.ل`);

    // 3. تحديث الأرباح في قاعدة البيانات
    console.log('\n💾 تحديث الأرباح في قاعدة البيانات...');

    const roundedProfit = Math.round(finalTotalProfit * 100) / 100;

    const updateStmt = db.prepare(`
      UPDATE cashbox
      SET profit_total = ?,
          updated_at = ?
      WHERE id = 1
    `);

    const updateResult = updateStmt.run(roundedProfit, new Date().toISOString());

    if (updateResult.changes > 0) {
      console.log(`   ✅ تم تحديث الأرباح بنجاح إلى: ${roundedProfit} د.ل`);

      // التحقق من التحديث
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      console.log(`   التحقق: الأرباح المحفوظة الآن: ${verifyResult.profit_total} د.ل`);
    } else {
      console.log('   ❌ فشل في تحديث الأرباح');
    }

    // 4. عرض ملخص الإصلاح
    console.log('\n📋 ملخص الإصلاح:');
    console.log('   ✅ تم إصلاح حساب الأرباح ليراعي مصاريف النقل بشكل صحيح');
    console.log('   ✅ مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط');
    console.log('   ✅ مصاريف النقل في البيع تخصم من الأرباح');
    console.log('   ✅ تم تحديث إجمالي الأرباح في الخزينة');

    console.log('\n🎉 تم الإصلاح بنجاح!');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error);
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الإصلاح
fixProfitCalculation().catch(console.error);
